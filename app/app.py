import os
import sys
import threading
import time
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional

# 设置环境编码，解决第三方库编码问题
os.environ["PYTHONIOENCODING"] = "utf-8"
os.environ["LC_ALL"] = "en_US.UTF-8"
os.environ["LANG"] = "en_US.UTF-8"

# 设置标准输出编码
if hasattr(sys.stdout, "reconfigure"):
    sys.stdout.reconfigure(encoding="utf-8")
if hasattr(sys.stderr, "reconfigure"):
    sys.stderr.reconfigure(encoding="utf-8")

import fastapi
import uvicorn
from agents.hr import get_agent_response_stream, graph
from configs.config import APP_KEY
from configs.lion_config import get_value
from fastapi import Body
from fastapi.responses import StreamingResponse
from langchain.schema import HumanMessage
from langchain_core.runnables import RunnableConfig
from monitoring.system_monitor import start_monitoring  # 系统监控
from my_mysql.entity.reminders import insert_reminder, query_reminders_by_status_and_time
from pycat import Cat
from pydantic import BaseModel
from service.enhanced_memory_service import EnhancedMemoryService  # 增强记忆服务
from service.ESmemory.es_memory_callback import search_memory
from service.mysql_person_service import add_person  # 改为MySQL人员服务
from service.mysql_person_service import (
    delete_person_mysql,
    ensure_user_profile_exists,
    get_all_persons_mysql,
    get_person_by_id_mysql,
    search_persons_by_name_mysql,
    update_person_mysql,
)
from service.recommendation_service import RecommendationService  # 推荐问题服务
from service.topic_recommendation_service import TopicRecommendationService  # 话题推荐服务
from utils.logger import logger

Cat.init_cat(APP_KEY)

# 全局变量用于追踪后台线程
reminder_thread = None
thread_start_time = None
news_crawler_thread = None

PROMPT_TEMPLATE = get_value(
    "reminder.prompt.template",
    "请根据以下信息生成一条温馨的提醒：{reminder_text_template}",
)


def reminder_background_task():
    """
    后台任务：每10分钟查询一次需要处理的提醒
    """
    # logger.info("后台提醒任务线程开始运行")
    while True:
        try:
            # 查询状态为'active'且触发时间已到的提醒
            current_time = datetime.now()  # TODO: 这里的时间需要弄好，决定好时间的格式，比如YYYY-MM-DD
            active_reminders = query_reminders_by_status_and_time("active", current_time)

            if active_reminders:
                # logger.info(f"发现 {len(active_reminders)} 条需要处理的提醒")
                columns = [
                    "reminder_id",
                    "user_id",
                    "subject_person_id",
                    "reminder_text_template",
                    "base_event_date",
                    "advance_notice_config",
                    "recurrence_rule",
                    "next_trigger_time",
                    "status",
                    "created_at",
                    "updated_at",
                ]
                for reminder in active_reminders:
                    reminder_dict = dict(zip(columns, reminder))
                    # logger.info(f"提醒ID: {reminder_dict['reminder_id']}, "
                    #           f"用户ID: {reminder_dict['user_id']}, "
                    #           f"触发时间: {reminder_dict['next_trigger_time']}")

                    # 1. 生成提醒内容
                    reminder_content = generate_reminder_content(reminder_dict)

                    # 2. 推送提醒
                    send_reminder_to_user(reminder_dict["user_id"], reminder_content)

                    # 3. 更新提醒状态为completed，避免重复推送
                    from my_mysql.entity.reminders import update_reminder_status

                    update_reminder_status(reminder_dict["reminder_id"], reminder_dict["user_id"], "completed")
            else:
                # logger.info("当前没有需要处理的提醒")
                pass
        except Exception as e:
            logger.error(f"后台提醒任务执行失败: {e}")

        # 休眠10分钟 (600秒)
        time.sleep(600)


def start_reminder_thread():
    """
    启动提醒后台线程
    """
    global reminder_thread, thread_start_time

    if reminder_thread and reminder_thread.is_alive():
        logger.warning("提醒线程已经在运行中")
        return False

    reminder_thread = threading.Thread(target=reminder_background_task, daemon=True)
    reminder_thread.start()
    thread_start_time = datetime.now()
    logger.info("后台提醒检查线程已启动")
    return True


def is_reminder_thread_alive():
    """
    检查提醒线程是否还活着
    """
    global reminder_thread
    return reminder_thread is not None and reminder_thread.is_alive()


def start_news_crawler_thread():
    """启动新闻爬虫后台线程"""
    global news_crawler_thread

    # 检查是否已有线程在运行
    if news_crawler_thread and news_crawler_thread.is_alive():
        logger.warning("新闻爬虫线程已经在运行中，跳过启动")
        return False

    try:
        from service.ESmemory.es_news_crawler import main_loop

        news_crawler_thread = threading.Thread(target=main_loop, daemon=True, name="NewsCrawlerThread")
        news_crawler_thread.start()
        logger.info("新闻爬虫定时线程已启动")
        return True
    except Exception as e:
        logger.error(f"新闻爬虫线程启动失败: {e}")
        raise


app = fastapi.FastAPI()

index_name = get_value("humanrelation.memory_index_name", "default_memory_index")

# 初始化增强记忆服务
enhanced_memory = EnhancedMemoryService()

# 初始化推荐问题服务
recommendation_service = RecommendationService()

# 初始化话题推荐服务
topic_recommendation_service = TopicRecommendationService()


class SearchMemoryRequest(BaseModel):
    user_input: str  # 用户输入内容，根据语义相关性查询
    size: int  # 返回结果数量
    k: int  # 候选数量
    user_id: str  # 查询者的用户 mis id
    memory_type: str  # 记忆类型，分long or short


class ChatRequest(BaseModel):
    content: str  # 用户问题
    conversation_id: str  # 对话 ID，用于上下文关联
    user_id: str  # 用户ID


class AddMemoryRequest(BaseModel):
    conversation_text: str  # 对话文本
    user_id: str  # 用户ID


@app.get("/")
def read_root():
    return "Hello World"


class ReadMemoryRequest(BaseModel):
    query_text: str  # 查询文本
    user_id: str  # 用户ID
    max_results: int = 10  # 最大结果数


class PersonRequest(BaseModel):
    user_id: str  # 用户ID
    canonical_name: str  # 正式姓名
    aliases: str = ""  # 别名
    relationships: list = []  # 人际关系
    profile_summary: str = ""  # 个人简介
    key_attributes: dict = {}  # 关键属性
    avatar: str = ""  # 头像URL
    is_user: bool = False  # 是否为用户


class MergePersonsRequest(BaseModel):
    user_id: str  # 用户ID
    primary_person_id: str
    secondary_person_id: str


class GetWeatherRequest(BaseModel):
    user_id: str  # 用户ID（用于权限验证）
    person_id: str  # 人员ID


@app.get("/humanrelation/reminder_thread_status")
def check_reminder_thread_status():
    """
    监察接口：检查后台提醒线程状态
    """
    is_alive = is_reminder_thread_alive()
    status_message = "线程正常运行" if is_alive else "线程已停止"
    start_time_str = thread_start_time.strftime("%Y-%m-%d %H:%M:%S") if thread_start_time else None

    result = {
        "is_alive": is_alive,
        "start_time": start_time_str,
        "status_message": status_message,
    }
    return result


@app.post("/humanrelation/restart_reminder_thread")
def restart_reminder_thread():
    """
    重启后台提醒线程
    """
    try:
        success = start_reminder_thread()
        if success:
            return {"success": True, "message": "后台提醒线程重启成功"}
        else:
            return {"success": False, "message": "线程已在运行中，无需重启"}
    except Exception as e:
        logger.error(f"重启提醒线程失败: {e}")
        return {"success": False, "message": f"重启失败: {str(e)}"}


@app.post("/humanrelation/search_memory")
def search_memory_endpoint(request: SearchMemoryRequest):
    result = search_memory(
        index_name=memory_index_name,
        user_input=request.user_input,
        size=request.size,
        k=request.k,
        user_id=request.user_id,
        memory_type=request.memory_type,
    )
    return result


class SearchMemoryByPersonIdRequest(BaseModel):
    user_id: str
    person_id: str


@app.post("/humanrelation/search_memory_by_person_id", summary="根据人员ID搜索记忆")
def search_memory_by_person_id_endpoint(request: SearchMemoryByPersonIdRequest):
    """根据人员ID搜索记忆"""
    from service.ESmemory.es_event_service import search_events_by_participant

    event_index = get_value("humanrelation.event_index_name", "memory_event_store")
    return search_events_by_participant(event_index, request.user_id, request.person_id)


# 根据userID获取对用的所有短期记忆
@app.get(
    "/humanrelation/get_all_short_memory_by_user_id",
    summary="根据userID获取对用的所有短期记忆",
)
def get_all_short_memory_by_user_id_endpoint(user_id: str, size: int = 100):
    """根据userID获取对用的所有短期记忆"""
    from service.ESmemory.es_event_service import get_recent_events

    event_index = get_value("humanrelation.event_index_name", "memory_event_store")
    return get_recent_events(event_index, user_id, size)


# 根据userID获取所有的person_id
@app.get(
    "/humanrelation/get_all_person_id_by_user_id",
    summary="根据userID获取所有的person_id",
)
def get_all_person_id_by_user_id_endpoint(user_id: str):
    """根据userID获取所有的person_id"""
    try:
        persons_result = get_all_persons_mysql(user_id, limit=1000, offset=0)
        if persons_result.get("result") == "success":
            person_ids = [person["person_id"] for person in persons_result.get("persons", [])]
            return {
                "result": "success",
                "person_ids": person_ids,
                "count": len(person_ids),
            }
        else:
            return {
                "result": "error",
                "reason": persons_result.get("reason", "未知错误"),
                "person_ids": [],
            }
    except Exception as e:
        logger.error(f"获取所有person_id失败: {str(e)}")
        return {"result": "error", "reason": str(e), "person_ids": []}


@app.post("/humanrelation/chat", summary="流式聊天接口")
def chat_endpoint(request: ChatRequest):
    """流式聊天接口，使用 conversation_id 维护上下文"""
    # 确保用户本人档案存在
    ensure_user_profile_exists(request.user_id)

    # 优化流式响应配置
    headers = {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Connection": "keep-alive",
        "X-Accel-Buffering": "no",  # 禁用nginx缓冲
        "X-Unbuffered": "true",  # 禁用代理缓冲
        "Transfer-Encoding": "chunked",  # 强制分块传输
    }

    response_stream = get_agent_response_stream(request.content, request.conversation_id, request.user_id)
    return StreamingResponse(response_stream, media_type="text/event-stream", headers=headers)


@app.post("/humanrelation/chat_json")
def chat_json_endpoint(request: ChatRequest):
    """非流式聊天接口，返回JSON格式，包含推荐问题"""
    try:
        # 确保用户本人档案存在
        ensure_user_profile_exists(request.user_id)

        # 使用同步方式调用记忆服务
        inputs = {
            "messages": [HumanMessage(content=request.content)],
            "user_id": request.user_id,
        }
        config: RunnableConfig = {
            "configurable": {
                "thread_id": request.conversation_id,
                "user_id": request.user_id,
            }
        }

        # 同步调用图
        result = graph.invoke(inputs, config=config)

        # 提取AI回复
        if result and "messages" in result and result["messages"]:
            ai_message = result["messages"][-1]
            ai_content = ai_message.content if hasattr(ai_message, "content") else str(ai_message)

            # 生成推荐问题
            recommended_questions = []
            try:
                # 检查是否启用推荐问题功能
                enable_recommendations_config = get_value("humanrelation.enable_recommendations", "true")
                enable_recommendations = str(enable_recommendations_config).lower() == "true"
                logger.info(f"推荐问题功能配置: {enable_recommendations_config}, 启用状态: {enable_recommendations}")

                if enable_recommendations:
                    logger.info("开始生成推荐问题...")
                    recommended_questions = recommendation_service.generate_recommended_questions(
                        user_input=request.content,
                        ai_response=ai_content,
                        user_id=request.user_id,
                        conversation_context="",  # 可以从config中获取更多上下文
                        max_questions=3,
                    )
                    logger.info(f"成功生成 {len(recommended_questions)} 个推荐问题")
                else:
                    logger.info("推荐问题功能已禁用")
            except Exception as e:
                logger.error(f"生成推荐问题失败: {str(e)}")
                import traceback

                logger.error(f"详细错误信息: {traceback.format_exc()}")
                # 推荐问题生成失败不影响主要功能

            return {"response": ai_content, "status": "success", "recommended_questions": recommended_questions}
        else:
            return {"response": "抱歉，我暂时无法回答您的问题。", "status": "error", "recommended_questions": []}
    except Exception as e:
        return {"response": f"处理请求时发生错误: {str(e)}", "status": "error", "recommended_questions": []}


@app.post("/humanrelation/add_memory", summary="添加记忆")
def add_memory(request: AddMemoryRequest):
    """从对话文本中提取并存储人员和事件记忆"""
    # 确保用户本人档案存在
    ensure_user_profile_exists(request.user_id)

    return enhanced_memory.extract_and_process_memory(request.conversation_text, request.user_id)


@app.post("/humanrelation/read_memory", summary="读取记忆")
def read_memory(request: ReadMemoryRequest):
    """根据查询文本检索相关的人员和事件记忆"""
    return enhanced_memory.retrieve_memory_for_conversation(request.query_text, request.user_id, request.max_results)


@app.get("/humanrelation/persons", summary="查看所有人列表")
def get_persons(user_id: str, limit: int = 100, offset: int = 0):
    """获取所有人员列表"""
    return get_all_persons_mysql(user_id, limit, offset)


@app.get("/humanrelation/person/{person_id}", summary="查看某个人")
def get_person(user_id: str, person_id: str):
    """根据ID获取人员详细信息"""
    return get_person_by_id_mysql(user_id, person_id)


@app.delete("/humanrelation/person/{person_id}", summary="删除某个人")
def delete_person(user_id: str, person_id: str):
    """删除指定人员"""
    return delete_person_mysql(user_id, person_id)


@app.post("/humanrelation/add_person", summary="增加某个人")
def create_person(request: PersonRequest):
    """添加新人员"""
    return add_person(
        user_id=request.user_id,
        canonical_name=request.canonical_name,
        aliases=request.aliases,
        relationships=request.relationships,
        profile_summary=request.profile_summary,
        key_attributes=request.key_attributes,
        avatar=request.avatar,
        is_user=request.is_user,
    )


@app.put("/humanrelation/change_person/{person_id}", summary="修改某个人的档案")
def update_person(person_id: str, request: PersonRequest):
    """更新人员档案信息"""
    return update_person_mysql(
        user_id=request.user_id,
        person_id=person_id,
        canonical_name=request.canonical_name,
        aliases=request.aliases,
        relationships=request.relationships,
        profile_summary=request.profile_summary,
        key_attributes=request.key_attributes,
        avatar=request.avatar,
        is_user=request.is_user,
    )


@app.get("/humanrelation/search_person", summary="搜索某个人")
def search_person(user_id: str, name: str, limit: int = 10):
    """按姓名搜索人员"""
    return search_persons_by_name_mysql(user_id, name, limit)


@app.post("/humanrelation/update_long_memory", summary="更新长期记忆")
def update_long_memory(user_id: str):
    """手动触发长期记忆更新，将短期记忆总结为人物小传"""
    return enhanced_memory.update_long_term_memory(user_id)


@app.get("/humanrelation/check_outdated", summary="检查过期信息")
def check_outdated_info(user_id: str, days: int = 180):
    """检查需要更新的过期状态信息"""
    return enhanced_memory.check_outdated_information(user_id, days)


@app.post("/humanrelation/merge_persons", summary="合并两个人员档案")
def merge_persons(request: MergePersonsRequest):
    """确认并执行两个人员档案的合并"""
    return enhanced_memory.execute_merge_persons(
        request.user_id, request.primary_person_id, request.secondary_person_id
    )


@app.get("/monitor/alive", summary="健康检查接口")
def health_check():
    """健康检查接口，用于部署时的存活性检查"""
    return {"status": "ok", "message": "Service is alive"}


@app.get("/humanrelation/get_user_profile", summary="获取用户本人档案")
def get_user_profile(user_id: str):
    """获取用户本人的档案信息"""
    from service.mysql_person_service import get_user_person

    user_person = get_user_person(user_id)
    if user_person:
        return {"result": "success", "person": user_person}
    else:
        return {"result": "error", "reason": "用户本人档案不存在"}


@app.get("/humanrelation/intimacy", summary="获取用户亲密度")
def get_user_intimacy(user_id: str):
    """获取用户与AI助手"老董"的亲密度信息"""
    from service.intimacy_service import IntimacyService

    intimacy_service = IntimacyService()
    return intimacy_service.get_intimacy_info(user_id)


@app.get("/humanrelation/get_user_profile_and_events", summary="获取用户本人档案与所有events")
def get_user_profile(user_id: str):
    """获取用户本人的档案信息"""
    from service.mysql_person_service import get_user_person

    user_person = get_user_person(user_id)
    person_id = user_person["person_id"]
    from service.ESmemory.es_event_service import search_events_by_participant

    event_index = get_value("humanrelation.event_index_name", "memory_event_store")
    event_list = search_events_by_participant(event_index, user_id, person_id)
    if user_person:
        return {"result": "success", "person": user_person, "events": event_list}
    else:
        return {"result": "error", "reason": "用户本人档案不存在"}


@app.get("/humanrelation/get_weather_related_info", summary="获取与天气相关的人物档案和事件")
def get_weather_related_info(user_id: str):
    """获取与天气相关的人物档案和事件信息，用于天气播报"""
    from service.weather_related_service import get_weather_related_persons_and_events

    try:
        result = get_weather_related_persons_and_events(user_id)
        return result
    except Exception as e:
        logger.error(f"获取天气相关信息失败: {e}")
        return {"result": "error", "reason": f"获取天气相关信息失败: {str(e)}"}


@app.get("/humanrelation/conversation_changes", summary="获取对话变化")
def get_conversation_changes(user_id: str, conversation_id: str):
    """
    获取指定对话中的所有变化：
    - 用户档案更新的具体内容
    - 新增的事件
    - 亲密度变化量
    """
    try:
        logger.info(f"开始获取对话变化: user_id={user_id}, conversation_id={conversation_id}")

        from configs.lion_config import get_value
        from service.ESmemory.es_event_service import get_events_by_conversation

        # 从 chat_history 表获取记录的变化
        if not graph.checkpointer:
            logger.error("graph.checkpointer 不存在")
            return {"result": "error", "reason": "系统未初始化"}

        logger.info("开始从数据库获取对话变化")
        changes = graph.checkpointer.get_conversation_changes(user_id, conversation_id)
        logger.info(f"数据库返回的变化: {changes}")

        if not changes:
            return {
                "result": "success",
                "conversation_id": conversation_id,
                "profile_updates": {},
                "new_events": [],
                "intimacy_increase": 0,
                "has_updates": False,
            }

        # 获取事件详情
        event_details = []
        if changes.get("events_added"):
            # 直接使用保存在数据库中的event_ids来获取事件详情
            event_ids = changes["events_added"]

            if event_ids:
                # 根据event_id获取具体的事件信息
                from service.ESmemory.es_event_service import get_event_by_id

                event_index = get_value("humanrelation.event_index_name", "memory_event_store")

                for event_id in event_ids:
                    event_result = get_event_by_id(event_index, user_id, event_id)
                    if event_result.get("result") == "success" and event_result.get("event"):
                        event_data = event_result["event"]
                        event_details.append(
                            {
                                "event_id": event_data.get("event_id"),
                                "description_text": event_data.get("description_text", ""),
                                "participants": event_data.get("participants", []),
                                "location": event_data.get("location", ""),
                                "topics": event_data.get("topics", []),
                                "sentiment": event_data.get("sentiment", ""),
                                "timestamp": event_data.get("timestamp"),
                            }
                        )

                logger.info(f"获取到{len(event_details)}个新增事件详情")

        # 格式化档案更新为中文显示
        from utils.profile_diff import format_profile_updates_for_display

        formatted_updates = format_profile_updates_for_display(changes.get("profile_updates", {}))

        has_updates = bool(formatted_updates or event_details or changes.get("intimacy_change", 0) > 0)

        return {
            "result": "success",
            "conversation_id": conversation_id,
            "profile_updates": formatted_updates,
            "new_events": event_details,
            "intimacy_increase": changes.get("intimacy_change", 0),
            "has_updates": has_updates,
        }

    except Exception as e:
        logger.error(f"获取对话变化失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


@app.get("/humanrelation/history", summary="获取聊天记录")
def get_history(conversation_id: str, user_id: str):
    """根据对话ID获取完整的聊天记录"""
    try:
        # graph.checkpointer is now an instance of MySQLCheckpointSaver
        if not graph.checkpointer:
            return {"status": "error", "message": "Checkpointer not initialized"}
        history = graph.checkpointer.get_history(conversation_id, user_id)  # type: ignore
        return {
            "conversation_id": conversation_id,
            "history": history,
            "status": "success",
        }
    except Exception as e:
        logger.error(f"获取聊天记录失败: conversation_id={conversation_id}, error={e}")
        return {"status": "error", "message": str(e)}


class CreateConversationRequest(BaseModel):
    user_id: str


class DeleteConversationRequest(BaseModel):
    user_id: str
    conversation_id: str


@app.post("/humanrelation/create_conversation", summary="生成新的会话ID")
def create_conversation_endpoint(request: CreateConversationRequest):
    """
    为新对话生成一个唯一的、带时间戳的 conversation_id.
    """
    try:
        user_id = request.user_id
        # 对user_id进行简化处理，确保生成的ID格式清晰
        safe_user_id = "".join(e for e in user_id if e.isalnum())
        timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        conversation_id = f"{safe_user_id}_{timestamp}"
        return {"success": True, "conversation_id": conversation_id}
    except Exception as e:
        logger.error(f"生成conversation_id失败: {e}")
        return {"success": False, "message": str(e)}


@app.get("/humanrelation/conversations", summary="获取用户的所有会话ID")
def get_user_conversations_endpoint(user_id: str, limit: int = 20, offset: int = 0):
    """
    根据用户ID获取其历史对话的 conversation_id 列表，包含聊天内容概括作为标题（支持分页）.

    Args:
        user_id: 用户ID
        limit: 每页返回的会话数量，默认20，最大100
        offset: 偏移量，默认0
    """
    import time

    start_time = time.time()

    try:
        if not graph.checkpointer:
            return {"success": False, "message": "Checkpointer not initialized"}

        # 限制每页最大数量，防止性能问题
        limit = min(limit, 100)

        # 记录数据库查询开始时间
        db_start_time = time.time()

        # 使用分页查询
        result = graph.checkpointer.get_conversations_with_summary_paginated(user_id, limit, offset)  # type: ignore

        db_end_time = time.time()

        # 计算总耗时
        total_time = time.time() - start_time
        db_time = db_end_time - db_start_time

        # 记录性能日志
        logger.info(f"🕐 分页接口性能统计 - 用户: {user_id}")
        logger.info(f"   📊 会话数量: {len(result['conversations'])}/{result['total_count']}")
        logger.info(f"   📄 分页信息: 第{result['current_page']}页，每页{result['page_size']}条")
        logger.info(f"   ⏱️  数据库查询耗时: {db_time:.3f}秒")
        logger.info(f"   ⏱️  接口总耗时: {total_time:.3f}秒")

        return {
            "success": True,
            "user_id": user_id,
            "summary": {conv["conversation_id"]: conv["summary"] for conv in result["conversations"]},
            "conversations": result["conversations"],
            "pagination": {
                "total_count": result["total_count"],
                "current_page": result["current_page"],
                "page_size": result["page_size"],
                "has_more": result["has_more"],
            },
            "performance": {
                "total_time_seconds": round(total_time, 3),
                "db_query_time_seconds": round(db_time, 3),
                "conversation_count": len(result["conversations"]),
            },
        }
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"获取 {user_id} 的会话列表失败 (耗时: {total_time:.3f}秒): {e}")
        return {"success": False, "message": str(e), "performance": {"total_time_seconds": round(total_time, 3)}}


@app.delete("/humanrelation/conversation", summary="删除历史对话")
def delete_conversation_endpoint(request: DeleteConversationRequest):
    """
    删除指定用户的指定对话历史记录

    Args:
        request: 包含用户ID和对话ID的请求对象

    Returns:
        删除结果，包含成功状态和消息
    """
    try:
        # 输入验证
        if not request.user_id or not request.user_id.strip():
            return {"success": False, "message": "用户ID不能为空"}

        if not request.conversation_id or not request.conversation_id.strip():
            return {"success": False, "message": "对话ID不能为空"}

        # 检查checkpointer是否初始化
        if not graph.checkpointer:
            return {"success": False, "message": "系统未初始化"}

        # 调用checkpointer的删除方法
        success = graph.checkpointer.delete_conversation(request.user_id.strip(), request.conversation_id.strip())  # type: ignore

        if success:
            logger.info(f"用户 {request.user_id} 成功删除对话: {request.conversation_id}")
            return {
                "success": True,
                "message": "对话删除成功",
                "user_id": request.user_id,
                "conversation_id": request.conversation_id,
            }
        else:
            logger.warning(f"用户 {request.user_id} 删除对话失败，对话可能不存在: {request.conversation_id}")
            return {"success": False, "message": "对话不存在或删除失败"}

    except Exception as e:
        logger.error(
            f"删除对话时发生错误: user_id={request.user_id}, conversation_id={request.conversation_id}, error={str(e)}"
        )
        return {"success": False, "message": f"删除对话时发生错误: {str(e)}"}


@app.on_event("startup")
async def startup_event():
    """
    应用启动事件：启动后台提醒检查线程和新闻爬虫线程
    """
    try:
        started = start_reminder_thread()
        if started:
            logger.info("应用启动时，后台提醒检查线程已成功启动。")
        else:
            logger.info("应用启动时，后台提醒检查线程已在运行，无需重复启动。")
    except Exception as e:
        logger.error(f"应用启动时启动提醒线程失败: {e}")

    # 新闻爬虫线程
    try:
        # 确保配置已加载
        news_ai_config = get_value("xiaomei.humanrelation.news_ai_config", "{}")
        news_judgment_prompt = get_value("xiaomei.humanrelation.news_judgment_prompt", "")
        news_keyword_prompt = get_value("xiaomei.humanrelation.news_keyword_generation_prompt", "")

        # 记录配置状态
        logger.info(
            f"新闻AI配置状态: AI配置={bool(news_ai_config)}, 判断提示词={bool(news_judgment_prompt)}, 关键词提示词={bool(news_keyword_prompt)}"
        )

        # 启动新闻爬虫线程
        started = start_news_crawler_thread()
        if started:
            logger.info("应用启动时，新闻爬虫线程已成功启动。")
        else:
            logger.info("应用启动时，新闻爬虫线程已在运行，无需重复启动。")
    except Exception as e:
        logger.error(f"应用启动时启动新闻爬虫线程失败: {e}")

    # 启动长期记忆更新工作线程
    try:
        worker_started = enhanced_memory.start_long_term_memory_update_thread()
        if worker_started:
            logger.info("应用启动时，后台长期记忆更新工作线程已成功启动。")
        else:
            logger.info("应用启动时，后台长期记忆更新工作线程已在运行，无需重复启动。")
    except Exception as e:
        logger.error(f"应用启动时启动长期记忆更新工作线程失败: {e}")


class AddReminderRequest(BaseModel):
    user_id: str
    base_event_date: datetime
    next_trigger_time: datetime
    subject_person_id: Optional[str] = None
    reminder_text_template: Optional[str] = None
    advance_notice_config: Optional[dict] = None
    recurrence_rule: Optional[str] = None
    status: str = "active"


@app.post("/humanrelation/add_reminder", summary="添加提醒")
def add_reminder(request: AddReminderRequest = Body(...)):
    """
    添加一条提醒到MySQL reminders表
    """
    reminder_id = insert_reminder(
        user_id=request.user_id,
        base_event_date=request.base_event_date,
        next_trigger_time=request.next_trigger_time,
        subject_person_id=request.subject_person_id,
        reminder_text_template=request.reminder_text_template,
        advance_notice_config=request.advance_notice_config,
        recurrence_rule=request.recurrence_rule,
        status=request.status,
    )
    # 新增：打印插入数据库的内容
    logger.info(
        f"插入数据库内容: reminder_id={reminder_id}, user_id={request.user_id}, next_trigger_time={request.next_trigger_time}, base_event_date={request.base_event_date}, subject_person_id={request.subject_person_id}, reminder_text_template={request.reminder_text_template}, advance_notice_config={request.advance_notice_config}, recurrence_rule={request.recurrence_rule}, status={request.status}"
    )
    if reminder_id:
        return {"success": True, "reminder_id": reminder_id}
    else:
        return {"success": False, "message": "插入失败"}


class DeleteReminderRequest(BaseModel):
    user_id: str
    reminder_id: int


@app.post("/humanrelation/delete_reminder", summary="删除提醒")
def delete_reminder(request: DeleteReminderRequest = Body(...)):
    """
    删除指定用户的提醒，确保用户隔离
    """
    from my_mysql.entity.reminders import delete_reminder_by_id

    success = delete_reminder_by_id(request.reminder_id, request.user_id)
    if success:
        return {"success": True, "message": "删除成功"}
    else:
        return {"success": False, "message": "删除失败或提醒不存在"}


@app.get("/humanrelation/list_reminders", summary="获取用户提醒列表")
def list_reminders(user_id: str):
    """
    获取指定用户的所有提醒列表，确保用户隔离
    """
    from my_mysql.entity.reminders import query_reminders_by_user

    try:
        reminders_raw = query_reminders_by_user(user_id)

        # 将 SQLAlchemy Row 对象转换为字典
        reminders = []
        for reminder in reminders_raw:
            if hasattr(reminder, "_asdict"):
                # 如果是 NamedTuple 类型
                reminder_dict = reminder._asdict()
            elif hasattr(reminder, "keys"):
                # 如果是 Row 对象
                reminder_dict = dict(reminder)
            else:
                # 如果已经是字典
                reminder_dict = reminder

            # 处理 JSON 字段
            if "advance_notice_config" in reminder_dict and reminder_dict["advance_notice_config"]:
                try:
                    import json

                    reminder_dict["advance_notice_config"] = json.loads(reminder_dict["advance_notice_config"])
                except:
                    pass  # 如果解析失败，保持原值

            reminders.append(reminder_dict)

        return {"success": True, "reminders": reminders, "count": len(reminders)}
    except Exception as e:
        logger.error(f"获取用户提醒列表失败: {str(e)}")
        return {"success": False, "message": f"获取提醒列表失败: {str(e)}", "reminders": [], "count": 0}


def generate_reminder_content(reminder_dict):
    # 直接使用原始的提醒内容，不再调用大模型生成
    reminder_text = reminder_dict["reminder_text_template"]
    if not reminder_text:
        return "提醒时间到了！"

    # 添加时间信息到提醒内容
    base_event_date = reminder_dict.get("base_event_date")
    if base_event_date:
        # 格式化时间显示
        if isinstance(base_event_date, datetime):
            time_str = base_event_date.strftime("%H:%M")
        else:
            # 如果是字符串，尝试解析
            try:
                parsed_time = datetime.strptime(str(base_event_date), "%Y-%m-%d %H:%M:%S")
                time_str = parsed_time.strftime("%H:%M")
            except:
                time_str = str(base_event_date)

        return f"⏰ 提醒：{reminder_text}（时间：{time_str}）"

    # 如果没有时间信息，返回原始内容
    return f"⏰ 提醒：{reminder_text}"


def send_reminder_to_user(user_id, content):
    # 这里可以是发微信、短信、App通知等
    logger.info(f"推送给用户{user_id}的提醒内容：{content}")


class UpdateEventRequest(BaseModel):
    user_id: str
    event_id: str
    description_text: Optional[str] = None
    participants: Optional[list] = None
    location: Optional[str] = None
    topics: Optional[list] = None
    sentiment: Optional[str] = None


@app.put("/humanrelation/update_event", summary="更新事件内容")
def update_event_endpoint(request: UpdateEventRequest):
    """更新事件内容，支持修正AI编辑错误和关联错误"""
    try:
        from configs.lion_config import get_value
        from service.ESmemory.es_event_service import update_event_content

        # 获取ES索引名称，与其他接口保持一致
        event_index = get_value("humanrelation.event_index_name", "memory_event_store")

        # 构建更新参数
        update_params = {}
        if request.description_text is not None:
            update_params["description_text"] = request.description_text
        if request.participants is not None:
            update_params["participants"] = request.participants
        if request.location is not None:
            update_params["location"] = request.location
        if request.topics is not None:
            update_params["topics"] = request.topics
        if request.sentiment is not None:
            update_params["sentiment"] = request.sentiment

        # 调用更新函数
        result = update_event_content(
            index_name=event_index, user_id=request.user_id, event_id=request.event_id, **update_params
        )

        if result:
            return {
                "success": True,
                "message": "事件更新成功",
                "event_id": request.event_id,
                "updated_fields": list(update_params.keys()),
            }
        else:
            return {"success": False, "message": "事件更新失败，可能事件不存在或无权限修改"}

    except Exception as e:
        logger.error(f"更新事件失败: {e}")
        return {"success": False, "message": f"更新事件时发生错误: {str(e)}"}


# 新闻爬取与统计接口优化


@app.post("/humanrelation/news/crawl", summary="手动触发新闻爬取")
def trigger_news_crawler():
    """手动触发新闻爬取和AI处理"""
    try:
        from service.ESmemory.es_news_crawler import main

        # 异步执行爬取任务
        t = threading.Thread(target=main, daemon=True)
        t.start()

        return {"success": True, "message": "新闻爬取任务已触发，正在后台执行"}
    except Exception as e:
        logger.error(f"触发新闻爬取失败: {e}")
        return {"success": False, "message": f"触发失败: {str(e)}"}


@app.get("/humanrelation/news/crawler/status", summary="查看新闻爬虫状态")
def get_news_crawler_status():
    """查看新闻爬虫线程状态"""
    global news_crawler_thread

    try:
        from configs.lion_config import get_value

        # 获取配置的爬取间隔
        crawl_interval = get_value("xiaomei.humanrelation.news_crawl_interval", "360")

        status = {
            "thread_exists": news_crawler_thread is not None,
            "thread_alive": news_crawler_thread.is_alive() if news_crawler_thread else False,
            "thread_name": news_crawler_thread.name if news_crawler_thread else None,
            "crawl_interval_minutes": crawl_interval,
            "crawl_interval_hours": float(crawl_interval) / 60,
        }

        return {"success": True, "status": status}
    except Exception as e:
        logger.error(f"获取新闻爬虫状态失败: {e}")
        return {"success": False, "message": f"获取状态失败: {str(e)}"}


@app.get("/humanrelation/news/stats", summary="查看新闻统计")
def get_news_stats():
    """查看AI处理的新闻统计信息"""
    try:
        from service.ESmemory.es_memory_client import client

        index_name = get_value("humanrelation.news_index_name", "humanrelation_news_v3")

        # 查询总数
        total_query = {"match_all": {}}
        total_resp = client.count(index=index_name, body={"query": total_query})
        total_count = total_resp.get("count", 0)

        # 查询AI判断为适合的数量
        suitable_query = {"term": {"ai_judgment.suitable": True}}
        suitable_resp = client.count(index=index_name, body={"query": suitable_query})
        suitable_count = suitable_resp.get("count", 0)

        # 查询AI判断为不适合的数量
        unsuitable_query = {"term": {"ai_judgment.suitable": False}}
        unsuitable_resp = client.count(index=index_name, body={"query": unsuitable_query})
        unsuitable_count = unsuitable_resp.get("count", 0)

        # 查询有关键词的数量
        keywords_query = {"exists": {"field": "generated_keywords"}}
        keywords_resp = client.count(index=index_name, body={"query": keywords_query})
        keywords_count = keywords_resp.get("count", 0)

        # 查询最近添加的新闻
        recent_resp = client.search(
            index=index_name, body={"query": {"match_all": {}}, "size": 10, "sort": [{"created_at": {"order": "desc"}}]}
        )

        recent_news = []
        for hit in recent_resp["hits"]["hits"]:
            source = hit["_source"]
            recent_news.append(
                {
                    "id": hit["_id"],
                    "title": source.get("title", ""),
                    "created_at": source.get("created_at", ""),
                    "suitable": source.get("ai_judgment", {}).get("suitable", None),
                    "confidence": source.get("ai_judgment", {}).get("confidence", None),
                    "keywords_count": len(source.get("generated_keywords", [])),
                    "source_keyword": source.get("source_keyword", ""),
                }
            )

        return {
            "success": True,
            "stats": {
                "total": total_count,
                "suitable": suitable_count,
                "unsuitable": unsuitable_count,
                "with_keywords": keywords_count,
                "suitable_percentage": round(suitable_count / total_count * 100, 2) if total_count > 0 else 0,
            },
            "recent_news": recent_news,
        }
    except Exception as e:
        logger.error(f"获取新闻统计失败: {e}")
        return {"success": False, "message": f"获取统计失败: {str(e)}"}


# 新闻搜索与处理接口


class NewsQueryRequest(BaseModel):
    """新闻搜索请求模型"""

    keyword: str = ""  # 搜索关键词
    page: int = 1  # 页码
    size: int = 10  # 每页条数
    tags: Optional[List[str]] = None  # 标签过滤
    start_date: Optional[str] = None  # 开始日期
    end_date: Optional[str] = None  # 结束日期
    use_semantic: bool = False  # 是否使用语义搜索


@app.post("/humanrelation/news/search", summary="搜索新闻")
def search_news(request: NewsQueryRequest):
    """搜索新闻接口，支持关键词、标签和时间范围过滤，以及语义搜索"""
    try:
        from rag_retriever import rag_retriever
        from service.ESmemory.es_memory_client import client

        index_name = get_value("humanrelation.news_index_name", "humanrelation_news_v3")

        # 判断是否使用语义搜索
        use_semantic = request.use_semantic if hasattr(request, "use_semantic") else False

        if use_semantic and request.keyword:
            logger.info(f"🔍 RAG: 使用语义搜索: '{request.keyword}'")
            results = rag_retriever.semantic_search(request.keyword, top_k=request.size)

            # 处理结果
            news_list = []
            for result in results:
                news_list.append(
                    {
                        "id": result.get("doc_id", ""),
                        "title": result.get("title", ""),
                        "content_preview": (
                            result.get("content", "")[:200] + "..."
                            if len(result.get("content", "")) > 200
                            else result.get("content", "")
                        ),
                        "tags": result.get("tags", []),
                        "created_at": result.get("created_at", ""),
                        "source_url": result.get("source_url", ""),
                        "similarity": result.get("similarity", 0),
                        "ai_judgment": result.get("ai_judgment", {}),
                        "generated_keywords": result.get("generated_keywords", []),
                        "source_keyword": result.get("source_keyword", ""),
                    }
                )

            return {"success": True, "total": len(news_list), "news": news_list, "search_type": "semantic"}
            # 处理结果
            news_list = []
            for result in results:
                news_list.append(
                    {
                        "id": result.get("doc_id", ""),
                        "title": result.get("title", ""),
                        "content_preview": (
                            result.get("content", "")[:200] + "..."
                            if len(result.get("content", "")) > 200
                            else result.get("content", "")
                        ),
                        "tags": result.get("tags", []),
                        "created_at": result.get("created_at", ""),
                        "source_url": result.get("source_url", ""),
                        "similarity": result.get("similarity", 0),
                        "ai_judgment": result.get("ai_judgment", {}),
                        "generated_keywords": result.get("generated_keywords", []),
                        "source_keyword": result.get("source_keyword", ""),
                    }
                )

            return {"success": True, "total": len(news_list), "news": news_list, "search_type": "semantic"}
        else:
            # 使用传统ES搜索
            # 构建查询
            query = {"bool": {"must": []}}

            # 关键词搜索
            if request.keyword:
                query["bool"]["must"].append(
                    {"multi_match": {"query": request.keyword, "fields": ["title^2", "content"]}}  # 标题权重更高
                )

            # 标签过滤
            if hasattr(request, "tags") and request.tags:
                if "filter" not in query["bool"]:
                    query["bool"]["filter"] = []
                query["bool"]["filter"].append({"terms": {"tags": request.tags}})

            # 时间范围过滤
            if (
                hasattr(request, "start_date")
                and hasattr(request, "end_date")
                and (request.start_date or request.end_date)
            ):
                date_filter = {"range": {"created_at": {}}}
                if request.start_date:
                    date_filter["range"]["created_at"]["gte"] = request.start_date
                if request.end_date:
                    date_filter["range"]["created_at"]["lte"] = request.end_date

                if "filter" not in query["bool"]:
                    query["bool"]["filter"] = []
                query["bool"]["filter"].append(date_filter)

            # 如果没有任何查询条件，默认查询所有
            if not query["bool"]["must"] and ("filter" not in query["bool"] or not query["bool"]["filter"]):
                query = {"match_all": {}}

            # 执行查询
            size = request.size if hasattr(request, "size") else 10
            page = request.page if hasattr(request, "page") else 1

            resp = client.search(
                index=index_name,
                body={
                    "query": query,
                    "size": size,
                    "from": (page - 1) * size,
                    "sort": [{"created_at": {"order": "desc"}}],
                },
            )

            # 处理结果
            total = resp["hits"]["total"]["value"] if "total" in resp["hits"] else 0
            hits = resp["hits"]["hits"]

            news_list = []
            for hit in hits:
                source = hit["_source"]
                news_list.append(
                    {
                        "id": hit["_id"],
                        "title": source.get("title", ""),
                        "content_preview": (
                            source.get("content", "")[:200] + "..."
                            if len(source.get("content", "")) > 200
                            else source.get("content", "")
                        ),
                        "tags": source.get("tags", []),
                        "created_at": source.get("created_at", ""),
                        "source_url": source.get("source_url", ""),
                        "ai_judgment": source.get("ai_judgment", {}),
                        "generated_keywords": source.get("generated_keywords", []),
                        "source_keyword": source.get("source_keyword", ""),
                    }
                )

            return {
                "success": True,
                "total": total,
                "page": page,
                "size": size,
                "total_pages": (total + size - 1) // size if size > 0 else 0,
                "news": news_list,
                "search_type": "keyword",
            }
    except Exception as e:
        logger.error(f"搜索新闻失败: {e}")
        return {"success": False, "message": f"搜索失败: {str(e)}"}


# 关键词管理接口
@app.get("/humanrelation/keywords", summary="查看所有关键词")
def get_all_keywords():
    """获取当前配置的所有关键词列表"""
    try:
        from service.ESmemory.es_news_crawler import get_news_config

        # 获取当前配置
        config, keywords_config, ai_config = get_news_config()

        # 提取关键词列表
        keywords = []
        for item in keywords_config.get("keywords", []):
            keywords.append(
                {"keyword": item.get("keyword", ""), "tags": item.get("tags", []), "enabled": item.get("enabled", True)}
            )

        # 获取生成的关键词建议
        try:
            from service.ESmemory.es_memory_client import client

            keywords_index_name = "humanrelation_generated_keywords"

            # 查询未使用的关键词建议
            query = {"bool": {"must": [{"term": {"used": False}}]}}

            resp = client.search(
                index=keywords_index_name, body={"query": query, "size": 50, "sort": [{"timestamp": {"order": "desc"}}]}
            )

            suggestions = []
            for hit in resp["hits"]["hits"]:
                doc = hit["_source"]
                suggestions.append(
                    {
                        "id": hit["_id"],
                        "source_title": doc.get("source_title", ""),
                        "keywords": doc.get("generated_keywords", []),
                        "timestamp": doc.get("timestamp", ""),
                    }
                )
        except Exception as e:
            logger.error(f"获取关键词建议失败: {e}")
            suggestions = []

        return {
            "success": True,
            "keywords": keywords,
            "total": len(keywords),
            "suggestions": suggestions,
            "suggestions_count": len(suggestions),
        }
    except Exception as e:
        logger.error(f"获取关键词列表失败: {e}")
        return {"success": False, "message": f"获取失败: {str(e)}"}


class AddPersonEventRequest(BaseModel):
    user_id: str
    description_text: Optional[str] = None
    participants: Optional[list] = None
    location: Optional[str] = None
    topics: Optional[list] = None
    sentiment: Optional[str] = None


class DeletePersonEventRequest(BaseModel):
    user_id: str
    event_id: str


class TopicRecommendationRequest(BaseModel):
    user_id: str  # 请求用户ID（用于权限验证）
    person_id: str  # 目标人员ID（为谁生成话题推荐）
    context: str = ""  # 额外的上下文信息
    max_topics: int = 5  # 最大推荐话题数量，限制1-5个
    fast_mode: bool = False  # 快速模式，跳过AI调用直接返回备用话题


@app.post("/humanrelation/add_person_event", summary="添加指定人员事件")
def add_person_event_endpoint(request: AddPersonEventRequest):
    """添加指定人员事件"""
    try:
        # 输入验证
        if not request.user_id or not request.user_id.strip():
            return {"result": "error", "reason": "user_id不能为空"}

        # 至少需要有描述文本或参与者信息
        if not request.description_text and not request.participants:
            return {"result": "error", "reason": "描述文本和参与者信息至少需要提供一个"}

        from service.ESmemory.es_event_service import add_event

        # 使用配置中心的索引名称，保持与系统其他部分一致
        event_index = get_value("humanrelation.event_index_name", "memory_event_store")

        result = add_event(
            index_name=event_index,
            user_id=request.user_id.strip(),
            description_text=request.description_text or "",
            participants=request.participants or [],
            location=request.location or "",
            topics=request.topics or [],
            sentiment=request.sentiment or "",
        )

        if result.get("result") == "success":
            logger.info(f"用户 {request.user_id} 成功添加事件: {result.get('event_id')}")
        else:
            logger.error(f"用户 {request.user_id} 添加事件失败: {result.get('reason')}")

        return result

    except Exception as e:
        logger.error(f"添加人员事件失败: {str(e)}")
        return {"result": "error", "reason": f"添加事件失败: {str(e)}"}


@app.delete("/humanrelation/delete_person_event", summary="删除指定人员事件")
def delete_person_event_endpoint(request: DeletePersonEventRequest):
    """删除指定人员事件"""
    try:
        # 输入验证
        if not request.user_id or not request.user_id.strip():
            return {"result": "error", "reason": "user_id不能为空"}

        if not request.event_id or not request.event_id.strip():
            return {"result": "error", "reason": "event_id不能为空"}

        from service.ESmemory.es_event_service import delete_event

        # 使用配置中心的索引名称
        event_index = get_value("humanrelation.event_index_name", "memory_event_store")

        result = delete_event(
            index_name=event_index,
            user_id=request.user_id.strip(),
            event_id=request.event_id.strip(),
        )

        if result.get("result") == "success":
            logger.info(f"用户 {request.user_id} 成功删除事件: {request.event_id}")
        else:
            logger.error(f"用户 {request.user_id} 删除事件失败: {result.get('reason')}")

        return result

    except Exception as e:
        logger.error(f"删除人员事件失败: {str(e)}")
        return {"result": "error", "reason": f"删除事件失败: {str(e)}"}


# 根据用户信息中的地点获取天气
@app.post("/humanrelation/get_weather", summary="根据用户地点获取天气")
def get_weather_endpoint(request: GetWeatherRequest):
    """根据用户信息中的地点获取天气"""
    try:
        from service.ESmemory.es_event_service import get_weather_by_person

        result = get_weather_by_person(request.user_id, request.person_id)

        return result

    except Exception as e:
        logger.error(f"获取用户天气失败: {str(e)}")
        return {"result": "error", "reason": f"获取用户天气失败: {str(e)}"}


@app.get("/humanrelation/comprehensive_weather", summary="获取用户综合天气信息")
def get_comprehensive_weather(user_id: str):
    """
    获取用户综合天气信息，包括：
    1. 用户常用地址的天气
    2. 用户即将发生事件地址的天气
    3. 直属亲属地址的天气
    4. 老板地址的天气
    5. AI生成的温和个性化天气提醒
    """
    try:
        from service.comprehensive_weather_service import ComprehensiveWeatherService

        weather_service = ComprehensiveWeatherService()
        result = weather_service.get_comprehensive_weather_info(user_id)

        if result.get("result") == "success":
            logger.info(f"用户 {user_id} 成功获取综合天气信息")
        else:
            logger.error(f"用户 {user_id} 获取综合天气信息失败: {result.get('reason')}")

        return result

    except Exception as e:
        logger.error(f"获取综合天气信息失败: {str(e)}")
        return {"result": "error", "reason": f"获取天气信息失败: {str(e)}"}


class GetPersonEventsRequest(BaseModel):
    user_id: str
    person_id: str
    page: int = 1
    page_size: int = 20


@app.get("/humanrelation/get_person_events", summary="获取某个人的事件列表")
def get_person_events_endpoint(user_id: str, person_id: str, page: int = 1, page_size: int = 20):
    """获取某个人参与的所有事件，支持分页"""
    try:
        # 输入验证
        if not user_id or not user_id.strip():
            return {"result": "error", "reason": "user_id不能为空"}

        if not person_id or not person_id.strip():
            return {"result": "error", "reason": "person_id不能为空"}

        # 验证分页参数
        page = max(1, page)  # 页码最小为1
        page_size = min(max(1, page_size), 20)  # 页大小在1-20之间

        from service.ESmemory.es_event_service import get_events_by_person_id

        # 使用配置中心的索引名称
        event_index = get_value("humanrelation.event_index_name", "memory_event_store")

        result = get_events_by_person_id(
            index_name=event_index, user_id=user_id.strip(), person_id=person_id.strip(), page=page, page_size=page_size
        )

        return result

    except Exception as e:
        logger.error(f"获取人员事件列表失败: {str(e)}")
        return {"result": "error", "reason": f"获取事件列表失败: {str(e)}"}


class UpdateReminderRequest(BaseModel):
    user_id: str
    reminder_id: int
    base_event_date: Optional[datetime] = None
    next_trigger_time: Optional[datetime] = None
    subject_person_id: Optional[str] = None
    reminder_text_template: Optional[str] = None
    advance_notice_config: Optional[dict] = None
    recurrence_rule: Optional[str] = None
    status: Optional[str] = None


@app.post("/humanrelation/update_reminder", summary="编辑提醒")
def update_reminder(request: UpdateReminderRequest = Body(...)):
    """
    编辑指定用户的提醒，确保用户隔离
    """
    from my_mysql.entity.reminders import update_reminder as db_update_reminder

    # 构建更新参数，只包含非None的字段
    update_params = {}
    if request.base_event_date is not None:
        update_params["base_event_date"] = request.base_event_date
    if request.next_trigger_time is not None:
        update_params["next_trigger_time"] = request.next_trigger_time
    if request.subject_person_id is not None:
        update_params["subject_person_id"] = request.subject_person_id
    if request.reminder_text_template is not None:
        update_params["reminder_text_template"] = request.reminder_text_template
    if request.advance_notice_config is not None:
        update_params["advance_notice_config"] = request.advance_notice_config
    if request.recurrence_rule is not None:
        update_params["recurrence_rule"] = request.recurrence_rule
    if request.status is not None:
        update_params["status"] = request.status

    success = db_update_reminder(request.reminder_id, request.user_id, **update_params)

    if success:
        logger.info(
            f"更新提醒成功: reminder_id={request.reminder_id}, user_id={request.user_id}, 更新字段={list(update_params.keys())}"
        )
        return {"result": "success"}
    else:
        return {"result": "error", "message": "更新失败或提醒不存在"}


@app.post("/humanrelation/recommend_topics", summary="根据人员档案和事件推荐话题")
def recommend_topics_endpoint(request: TopicRecommendationRequest):
    """
    根据指定人员档案和事件信息生成推荐话题

    功能：
    1. 基于任何person的档案生成个性化、生活化的话题推荐
    2. 结合person的近期事件和提醒生成有趣话题
    3. 提供适合日常聊天和社交的生活化话题
    4. 支持为用户本人或其他person生成话题
    """
    try:
        # 输入验证
        if not request.user_id or not request.user_id.strip():
            return {"result": "error", "reason": "user_id不能为空"}

        if not request.person_id or not request.person_id.strip():
            return {"result": "error", "reason": "person_id不能为空"}

        # 验证最大话题数量
        max_topics = min(max(1, request.max_topics), 5)  # 限制在1-5之间

        logger.info(f"开始为person {request.person_id} 生成话题推荐（请求用户: {request.user_id}）")
        logger.info(f"请求参数: context={request.context}, max_topics={max_topics}")

        # 调用话题推荐服务
        result = topic_recommendation_service.generate_recommended_topics(
            user_id=request.user_id.strip(),
            person_id=request.person_id.strip(),
            context=request.context,
            max_topics=max_topics,
            fast_mode=request.fast_mode,
        )

        if result.get("result") == "success":
            logger.info(
                f"成功为person {request.person_id} 生成话题推荐，生成数量: {len(result.get('recommended_topics', []))}"
            )
        else:
            logger.error(f"为person {request.person_id} 生成话题推荐失败: {result.get('reason')}")

        return result

    except Exception as e:
        logger.error(f"话题推荐接口异常: {str(e)}")
        return {
            "result": "error",
            "reason": f"生成话题推荐时发生错误: {str(e)}",
            "user_id": request.user_id,
            "person_id": request.person_id,
            "recommended_topics": [],
        }


if __name__ == "__main__":
    # 启动系统监控
    start_monitoring()
    logger.info("🚀 应用启动，系统监控已开启")

    uvicorn.run(app, host="0.0.0.0", port=8080)
